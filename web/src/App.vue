<script setup lang="ts">
import { ref, computed } from 'vue'
import { generateRequirement, generateDesign, generateProject, checkAIHealth } from './api/ai'

// 响应式数据
const userInput = ref('')
const isLoading = ref(false)
const currentStep = ref<'input' | 'requirement' | 'design' | 'project'>('input')
const requirementDescription = ref('')
const designAndTasks = ref('')
const projectName = ref('')
const projectResult = ref<{
  projectPath: string;
  message: string;
  files: string[];
} | null>(null)
const error = ref('')
const aiHealthy = ref(false)

// 计算属性
const canSubmit = computed(() => userInput.value.trim().length > 0 && !isLoading.value)
const canGenerateDesign = computed(() => requirementDescription.value.trim().length > 0 && !isLoading.value)
const canGenerateProject = computed(() =>
  designAndTasks.value.trim().length > 0 &&
  projectName.value.trim().length > 0 &&
  !isLoading.value
)

// 检查AI服务健康状态
const checkHealth = async () => {
  try {
    const response = await checkAIHealth()
    aiHealthy.value = response.success
  } catch (err) {
    aiHealthy.value = false
  }
}

// 生成需求描述
const handleGenerateRequirement = async () => {
  if (!canSubmit.value) return

  isLoading.value = true
  error.value = ''

  try {
    const response = await generateRequirement(userInput.value.trim())

    if (response.success && response.data) {
      requirementDescription.value = response.data.requirementDescription
      currentStep.value = 'requirement'
    } else {
      error.value = response.error || '生成需求描述失败'
    }
  } catch (err) {
    error.value = '网络错误，请检查服务器连接'
    console.error('Error generating requirement:', err)
  } finally {
    isLoading.value = false
  }
}

// 生成设计和任务
const handleGenerateDesign = async () => {
  if (!canGenerateDesign.value) return

  isLoading.value = true
  error.value = ''

  try {
    const response = await generateDesign(requirementDescription.value)

    if (response.success && response.data) {
      designAndTasks.value = response.data.designAndTasks
      currentStep.value = 'design'
    } else {
      error.value = response.error || '生成设计和任务失败'
    }
  } catch (err) {
    error.value = '网络错误，请检查服务器连接'
    console.error('Error generating design:', err)
  } finally {
    isLoading.value = false
  }
}

// 生成Vue3项目
const handleGenerateProject = async () => {
  if (!canGenerateProject.value) return

  isLoading.value = true
  error.value = ''

  try {
    const response = await generateProject(designAndTasks.value, projectName.value.trim())

    if (response.success && response.data) {
      projectResult.value = {
        projectPath: response.data.projectPath,
        message: response.data.message,
        files: response.data.files
      }
      currentStep.value = 'project'
    } else {
      error.value = response.error || '生成项目失败'
    }
  } catch (err) {
    error.value = '网络错误，请检查服务器连接'
    console.error('Error generating project:', err)
  } finally {
    isLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  userInput.value = ''
  requirementDescription.value = ''
  designAndTasks.value = ''
  projectName.value = ''
  projectResult.value = null
  error.value = ''
  currentStep.value = 'input'
}

// 页面加载时检查健康状态
checkHealth()
</script>

<template>
  <div class="app-container">
    <header class="header">
      <h1>AI 需求分析与任务生成器</h1>
      <div class="health-status" :class="{ healthy: aiHealthy, unhealthy: !aiHealthy }">
        <span class="status-dot"></span>
        {{ aiHealthy ? 'AI服务正常' : 'AI服务异常' }}
      </div>
    </header>

    <main class="main-content">
      <!-- 步骤指示器 -->
      <div class="steps">
        <div class="step" :class="{ active: currentStep === 'input', completed: currentStep !== 'input' }">
          <span class="step-number">1</span>
          <span class="step-title">输入需求</span>
        </div>
        <div class="step" :class="{ active: currentStep === 'requirement', completed: ['design', 'project'].includes(currentStep) }">
          <span class="step-number">2</span>
          <span class="step-title">需求分析</span>
        </div>
        <div class="step" :class="{ active: currentStep === 'design', completed: currentStep === 'project' }">
          <span class="step-number">3</span>
          <span class="step-title">设计与任务</span>
        </div>
        <div class="step" :class="{ active: currentStep === 'project' }">
          <span class="step-number">4</span>
          <span class="step-title">生成项目</span>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="error-message">
        {{ error }}
      </div>

      <!-- 第一步：输入需求 -->
      <div v-if="currentStep === 'input'" class="step-content">
        <div class="input-section">
          <h2>请描述您的需求</h2>
          <textarea
            v-model="userInput"
            placeholder="请用自然语言描述您想要实现的功能，例如：我想要一个用户管理系统，可以添加、编辑、删除用户信息..."
            rows="6"
            :disabled="isLoading"
          ></textarea>
          <div class="button-group">
            <button
              @click="handleGenerateRequirement"
              :disabled="!canSubmit"
              class="primary-button"
            >
              {{ isLoading ? '生成中...' : '生成需求描述' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 第二步：需求描述 -->
      <div v-if="currentStep === 'requirement'" class="step-content">
        <div class="result-section">
          <h2>需求分析结果</h2>
          <div class="content-display">
            <pre>{{ requirementDescription }}</pre>
          </div>
          <div class="button-group">
            <button @click="resetForm" class="secondary-button">重新开始</button>
            <button
              @click="handleGenerateDesign"
              :disabled="!canGenerateDesign"
              class="primary-button"
            >
              {{ isLoading ? '生成中...' : '生成设计和任务' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 第三步：设计和任务 -->
      <div v-if="currentStep === 'design'" class="step-content">
        <div class="result-section">
          <h2>设计方案与任务列表</h2>
          <div class="content-display">
            <pre>{{ designAndTasks }}</pre>
          </div>

          <div class="project-input-section">
            <h3>项目配置</h3>
            <div class="input-group">
              <label for="projectName">项目名称：</label>
              <input
                id="projectName"
                v-model="projectName"
                type="text"
                placeholder="请输入项目名称（如：my-blog-system）"
                :disabled="isLoading"
                class="project-name-input"
              />
            </div>
          </div>

          <div class="button-group">
            <button @click="resetForm" class="secondary-button">重新开始</button>
            <button
              @click="handleGenerateProject"
              :disabled="!canGenerateProject"
              class="primary-button"
            >
              {{ isLoading ? '生成中...' : '生成Vue3项目' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 第四步：项目生成结果 -->
      <div v-if="currentStep === 'project'" class="step-content">
        <div class="result-section">
          <h2>项目生成完成</h2>

          <div v-if="projectResult" class="project-result">
            <div class="success-message">
              <h3>✅ {{ projectResult.message }}</h3>
              <p><strong>项目路径：</strong>{{ projectResult.projectPath }}</p>
            </div>

            <div class="files-list">
              <h4>生成的文件列表：</h4>
              <ul>
                <li v-for="file in projectResult.files" :key="file">{{ file }}</li>
              </ul>
            </div>

            <div class="next-steps">
              <h4>下一步操作：</h4>
              <ol>
                <li>进入项目目录：<code>cd {{ projectResult.projectPath }}</code></li>
                <li>安装依赖：<code>pnpm install</code></li>
                <li>启动开发服务器：<code>pnpm dev</code></li>
              </ol>
            </div>
          </div>

          <div class="button-group">
            <button @click="resetForm" class="secondary-button">生成新项目</button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.header h1 {
  color: #1f2937;
  margin-bottom: 10px;
}

.health-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.health-status.healthy {
  background-color: #dcfce7;
  color: #166534;
}

.health-status.unhealthy {
  background-color: #fef2f2;
  color: #dc2626;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: currentColor;
}

.steps {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  gap: 40px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.step.active,
.step.completed {
  opacity: 1;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s;
}

.step.active .step-number {
  background-color: #3b82f6;
  color: white;
}

.step.completed .step-number {
  background-color: #10b981;
  color: white;
}

.step-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.step.active .step-title,
.step.completed .step-title {
  color: #1f2937;
}

.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.step-content {
  background-color: #f9fafb;
  border-radius: 12px;
  padding: 30px;
}

.input-section h2,
.result-section h2 {
  color: #1f2937;
  margin-bottom: 20px;
  text-align: center;
}

.input-section textarea {
  width: 100%;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.3s;
}

.input-section textarea:focus {
  outline: none;
  border-color: #3b82f6;
}

.input-section textarea:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.content-display {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.content-display pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #374151;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 20px;
}

.primary-button,
.secondary-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.primary-button {
  background-color: #3b82f6;
  color: white;
}

.primary-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.primary-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.secondary-button {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.secondary-button:hover {
  background-color: #e5e7eb;
}

.project-input-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.project-input-section h3 {
  margin-bottom: 15px;
  color: #1f2937;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-weight: 500;
  color: #374151;
}

.project-name-input {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.project-name-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.project-name-input:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

.project-result > * + * {
  margin-top: 20px;
}

.success-message {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.success-message h3 {
  color: #166534;
  margin-bottom: 8px;
}

.success-message p {
  color: #15803d;
  margin: 0;
}

.files-list {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.files-list h4 {
  color: #1f2937;
  margin-bottom: 12px;
}

.files-list ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.files-list li {
  padding: 4px 0;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.next-steps {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 16px;
}

.next-steps h4 {
  color: #1e40af;
  margin-bottom: 12px;
}

.next-steps ol {
  margin: 0;
  padding-left: 20px;
}

.next-steps li {
  margin-bottom: 8px;
  color: #1f2937;
}

.next-steps code {
  background-color: #1f2937;
  color: #f9fafb;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

@media (max-width: 768px) {
  .app-container {
    padding: 16px;
  }

  .steps {
    gap: 20px;
  }

  .step-content {
    padding: 20px;
  }

  .button-group {
    flex-direction: column;
  }

  .input-group {
    gap: 12px;
  }
}
</style>
