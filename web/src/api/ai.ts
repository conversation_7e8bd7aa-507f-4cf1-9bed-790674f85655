import axios from 'axios'

const API_BASE_URL = ''

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 60 * 1000 * 10,
  headers: {
    'Content-Type': 'application/json',
  },
})

export interface RequirementResponse {
  success: boolean
  data?: {
    input: string
    requirementDescription: string
    generatedAt: string
  }
  error?: string
  message?: string
}

export interface DesignResponse {
  success: boolean
  data?: {
    requirementDescription: string
    designAndTasks: string
    generatedAt: string
  }
  error?: string
  message?: string
}

export interface ProjectResponse {
  success: boolean
  data?: {
    projectName: string
    projectPath: string
    message: string
    files: string[]
    generatedAt: string
  }
  error?: string
  message?: string
}

/**
 * 生成需求描述
 */
export async function generateRequirement(input: string): Promise<RequirementResponse> {
  try {
    const response = await apiClient.post<RequirementResponse>('/ai/generate-requirement', {
      input,
    })
    return response.data
  } catch (error) {
    console.error('Failed to generate requirement:', error)
    throw error
  }
}

/**
 * 生成设计和任务
 */
export async function generateDesign(requirementDescription: string): Promise<DesignResponse> {
  try {
    const response = await apiClient.post<DesignResponse>('/ai/generate-design', {
      requirementDescription,
    })
    return response.data
  } catch (error) {
    console.error('Failed to generate design:', error)
    throw error
  }
}

/**
 * 生成Vue3项目
 */
export async function generateProject(designAndTasks: string, projectName: string): Promise<ProjectResponse> {
  try {
    const response = await apiClient.post<ProjectResponse>('/ai/generate-project', {
      designAndTasks,
      projectName,
    })
    return response.data
  } catch (error) {
    console.error('Failed to generate project:', error)
    throw error
  }
}

/**
 * 检查AI服务健康状态
 */
export async function checkAIHealth(): Promise<{ success: boolean; data?: any }> {
  try {
    const response = await apiClient.get('/ai/health')
    return response.data
  } catch (error) {
    console.error('Failed to check AI health:', error)
    throw error
  }
}
