{"name": "server", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@ai-sdk/openai": "^2.0.4", "@hono/node-server": "^1.18.1", "@hono/zod-validator": "^0.7.2", "@modelcontextprotocol/server-filesystem": "^2025.7.29", "@openrouter/ai-sdk-provider": "^1.1.0", "@pimzino/spec-workflow-mcp": "^0.0.7", "ai": "^5.0.6", "archiver": "^7.0.1", "hono": "^4.8.12", "nanoid": "^5.1.5", "pino": "^9.7.0", "pino-pretty": "^13.1.1", "zod": "^4.0.15"}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/node": "^20.11.17", "tsx": "^4.7.1", "typescript": "^5.8.3"}}