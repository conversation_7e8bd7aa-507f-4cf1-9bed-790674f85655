import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { registerUser, loginUser, refreshAccessToken, logoutUser, getCurrentUser, } from '../services/auth.service.js';
import { authMiddleware } from '../middleware/auth.middleware.js';
import { logger as pinoLogger } from '../utils/logger.js';
const logger = pinoLogger();
export const authRoutes = new Hono();
// 注册用户验证模式
const registerSchema = z.object({
    username: z.string().min(3).max(20).regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
    email: z.string().email('邮箱格式不正确'),
    password: z.string().min(8, '密码至少8位').max(128, '密码不能超过128位'),
});
// 登录验证模式
const loginSchema = z.object({
    email: z.string().email('邮箱格式不正确'),
    password: z.string().min(1, '密码不能为空'),
});
// 刷新令牌验证模式
const refreshTokenSchema = z.object({
    refreshToken: z.string().min(1, '刷新令牌不能为空'),
});
/**
 * POST /auth/register - 用户注册
 */
authRoutes.post('/register', zValidator('json', registerSchema), async (c) => {
    try {
        const userData = c.req.valid('json');
        logger.info({ username: userData.username, email: userData.email }, 'User registration attempt');
        const result = await registerUser(userData);
        return c.json({
            success: true,
            message: '注册成功',
            data: result,
        }, 201);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '注册失败';
        logger.error({ error }, 'User registration failed');
        return c.json({
            success: false,
            message: errorMessage,
        }, 400);
    }
});
/**
 * POST /auth/login - 用户登录
 */
authRoutes.post('/login', zValidator('json', loginSchema), async (c) => {
    try {
        const loginData = c.req.valid('json');
        logger.info({ email: loginData.email }, 'User login attempt');
        const result = await loginUser(loginData);
        return c.json({
            success: true,
            message: '登录成功',
            data: result,
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '登录失败';
        logger.error({ error, email: loginData.email }, 'User login failed');
        return c.json({
            success: false,
            message: errorMessage,
        }, 401);
    }
});
/**
 * POST /auth/refresh - 刷新访问令牌
 */
authRoutes.post('/refresh', zValidator('json', refreshTokenSchema), async (c) => {
    try {
        const { refreshToken } = c.req.valid('json');
        logger.info('Token refresh attempt');
        const result = await refreshAccessToken(refreshToken);
        return c.json({
            success: true,
            message: '令牌刷新成功',
            data: result,
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '令牌刷新失败';
        logger.error({ error }, 'Token refresh failed');
        return c.json({
            success: false,
            message: errorMessage,
        }, 401);
    }
});
/**
 * POST /auth/logout - 用户登出
 */
authRoutes.post('/logout', authMiddleware, async (c) => {
    try {
        const user = c.user; // authMiddleware确保用户存在
        logger.info({ userId: user.id, username: user.username }, 'User logout attempt');
        await logoutUser(user.id);
        return c.json({
            success: true,
            message: '登出成功',
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '登出失败';
        logger.error({ error, userId: c.user?.id }, 'User logout failed');
        return c.json({
            success: false,
            message: errorMessage,
        }, 500);
    }
});
/**
 * GET /auth/me - 获取当前用户信息
 */
authRoutes.get('/me', authMiddleware, async (c) => {
    try {
        const user = c.user; // authMiddleware确保用户存在
        logger.info({ userId: user.id, username: user.username }, 'Get current user info');
        const currentUser = await getCurrentUser(user.id);
        return c.json({
            success: true,
            message: '获取用户信息成功',
            data: currentUser,
        });
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取用户信息失败';
        logger.error({ error, userId: c.user?.id }, 'Get current user failed');
        return c.json({
            success: false,
            message: errorMessage,
        }, 500);
    }
});
/**
 * GET /auth/health - 认证服务健康检查
 */
authRoutes.get('/health', (c) => {
    return c.json({
        success: true,
        message: '认证服务运行正常',
        timestamp: new Date().toISOString(),
    });
});
