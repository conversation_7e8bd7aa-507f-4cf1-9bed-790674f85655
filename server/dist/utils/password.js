import bcrypt from 'bcryptjs';
/**
 * 加密密码
 */
export async function hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
}
/**
 * 验证密码
 */
export async function verifyPassword(password, hashedPassword) {
    return bcrypt.compare(password, hashedPassword);
}
/**
 * 验证密码强度
 */
export function validatePasswordStrength(password) {
    const errors = [];
    if (password.length < 8) {
        errors.push('密码长度至少8位');
    }
    if (password.length > 128) {
        errors.push('密码长度不能超过128位');
    }
    if (!/[a-z]/.test(password)) {
        errors.push('密码必须包含至少一个小写字母');
    }
    if (!/[A-Z]/.test(password)) {
        errors.push('密码必须包含至少一个大写字母');
    }
    if (!/\d/.test(password)) {
        errors.push('密码必须包含至少一个数字');
    }
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        errors.push('密码必须包含至少一个特殊字符');
    }
    return {
        isValid: errors.length === 0,
        errors,
    };
}
