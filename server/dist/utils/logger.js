import { pino } from 'pino';
let _logger;
/**
 * 获取全局单例 logger
 */
export function logger() {
    if (_logger)
        return _logger;
    _logger = pino({
        level: process.env.LOG_LEVEL ?? 'info',
        transport: process.env.NODE_ENV === 'production'
            ? undefined
            : {
                target: 'pino-pretty',
                options: {
                    colorize: true,
                    translateTime: 'SYS:standard',
                    ignore: 'pid,hostname',
                },
            },
    });
    return _logger;
}
