import { extractTokenFromHeader, verifyAccessToken } from '../utils/jwt.js';
import { getUserById } from '../services/user.service.js';
import { logger as pinoLogger } from '../utils/logger.js';
const logger = pinoLogger();
/**
 * JWT认证中间件
 */
export async function authMiddleware(c, next) {
    try {
        const authHeader = c.req.header('Authorization');
        const token = extractTokenFromHeader(authHeader);
        if (!token) {
            return c.json({ error: '缺少访问令牌' }, 401);
        }
        // 验证JWT令牌
        const payload = verifyAccessToken(token);
        // 获取用户信息
        const user = await getUserById(payload.userId);
        if (!user) {
            return c.json({ error: '用户不存在' }, 401);
        }
        // 将用户信息添加到上下文
        c.user = user;
        logger.debug({ userId: user.id, username: user.username }, 'User authenticated via JWT');
        await next();
    }
    catch (error) {
        logger.warn({ error: error instanceof Error ? error.message : 'Unknown error' }, 'Authentication failed');
        return c.json({ error: '无效的访问令牌' }, 401);
    }
}
/**
 * 可选的JWT认证中间件（不强制要求认证）
 */
export async function optionalAuthMiddleware(c, next) {
    try {
        const authHeader = c.req.header('Authorization');
        const token = extractTokenFromHeader(authHeader);
        if (token) {
            try {
                const payload = verifyAccessToken(token);
                const user = await getUserById(payload.userId);
                if (user) {
                    c.user = user;
                    logger.debug({ userId: user.id, username: user.username }, 'User authenticated via optional JWT');
                }
            }
            catch (error) {
                // 忽略认证错误，继续处理请求
                logger.debug({ error: error instanceof Error ? error.message : 'Unknown error' }, 'Optional authentication failed');
            }
        }
        await next();
    }
    catch (error) {
        logger.error({ error }, 'Error in optional auth middleware');
        await next();
    }
}
/**
 * 管理员权限中间件（示例，可根据需要扩展）
 */
export async function adminMiddleware(c, next) {
    if (!c.user) {
        return c.json({ error: '需要认证' }, 401);
    }
    // 这里可以添加管理员权限检查逻辑
    // 例如：检查用户角色、权限等
    // 目前简单地检查用户名是否为admin
    if (c.user.username !== 'admin') {
        return c.json({ error: '需要管理员权限' }, 403);
    }
    await next();
}
