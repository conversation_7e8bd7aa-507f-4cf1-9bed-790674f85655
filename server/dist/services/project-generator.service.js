import { promises as fs } from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { logger as pinoLogger } from '../utils/logger.js';
const execAsync = promisify(exec);
const logger = pinoLogger();
/**
 * 创建Vue3项目结构和代码文件
 */
export async function createVue3ProjectStructure(projectName, config, designAndTasks) {
    const projectPath = path.join(process.cwd(), 'generated-projects', projectName);
    const createdFiles = [];
    try {
        // 创建项目目录
        await fs.mkdir(projectPath, { recursive: true });
        logger.info({ projectPath }, 'Created project directory');
        // 创建基础项目结构
        const directories = [
            'src',
            'src/components',
            'src/views',
            'src/stores',
            'src/types',
            'src/api',
            'src/utils',
            'src/assets',
            'public'
        ];
        for (const dir of directories) {
            await fs.mkdir(path.join(projectPath, dir), { recursive: true });
        }
        // 生成package.json
        const packageJson = {
            name: projectName,
            version: '0.1.0',
            type: 'module',
            scripts: {
                dev: 'vite',
                build: 'vue-tsc && vite build',
                preview: 'vite preview'
            },
            dependencies: {
                vue: '^3.5.0',
                'vue-router': '^4.5.0',
                pinia: '^3.0.0',
                axios: '^1.7.0',
                ...Object.fromEntries(config.dependencies.map(dep => [dep, 'latest']))
            },
            devDependencies: {
                '@vitejs/plugin-vue': '^6.0.0',
                typescript: '^5.8.0',
                'vue-tsc': '^3.0.0',
                vite: '^6.0.0',
                ...Object.fromEntries(config.devDependencies.map(dep => [dep, 'latest']))
            }
        };
        await fs.writeFile(path.join(projectPath, 'package.json'), JSON.stringify(packageJson, null, 2));
        createdFiles.push('package.json');
        // 生成vite.config.ts
        const viteConfig = `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': '/src',
    },
  },
})`;
        await fs.writeFile(path.join(projectPath, 'vite.config.ts'), viteConfig);
        createdFiles.push('vite.config.ts');
        // 生成tsconfig.json
        const tsConfig = {
            compilerOptions: {
                target: 'ES2020',
                useDefineForClassFields: true,
                lib: ['ES2020', 'DOM', 'DOM.Iterable'],
                module: 'ESNext',
                skipLibCheck: true,
                moduleResolution: 'bundler',
                allowImportingTsExtensions: true,
                resolveJsonModule: true,
                isolatedModules: true,
                noEmit: true,
                jsx: 'preserve',
                strict: true,
                noUnusedLocals: true,
                noUnusedParameters: true,
                noFallthroughCasesInSwitch: true,
                baseUrl: '.',
                paths: {
                    '@/*': ['./src/*']
                }
            },
            include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
            references: [{ path: './tsconfig.node.json' }]
        };
        await fs.writeFile(path.join(projectPath, 'tsconfig.json'), JSON.stringify(tsConfig, null, 2));
        createdFiles.push('tsconfig.json');
        // 生成index.html
        const indexHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>${projectName}</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>`;
        await fs.writeFile(path.join(projectPath, 'index.html'), indexHtml);
        createdFiles.push('index.html');
        // 生成main.ts
        const mainTs = `import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './style.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.mount('#app')`;
        await fs.writeFile(path.join(projectPath, 'src/main.ts'), mainTs);
        createdFiles.push('src/main.ts');
        // 生成基础样式文件
        const styleCss = `* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
}

#app {
  min-height: 100vh;
}`;
        await fs.writeFile(path.join(projectPath, 'src/style.css'), styleCss);
        createdFiles.push('src/style.css');
        // 生成类型定义文件
        const typesContent = config.types.length > 0
            ? config.types.map(type => type.interface).join('\n\n')
            : `export interface User {
  id: string;
  name: string;
  email: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}`;
        await fs.writeFile(path.join(projectPath, 'src/types/index.ts'), typesContent);
        createdFiles.push('src/types/index.ts');
        // 生成路由文件
        const routerContent = `import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue')
    }
  ]
})

export default router`;
        await fs.writeFile(path.join(projectPath, 'src/router/index.ts'), routerContent);
        createdFiles.push('src/router/index.ts');
        // 创建router目录
        await fs.mkdir(path.join(projectPath, 'src/router'), { recursive: true });
        // 生成App.vue
        const appVue = `<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
// App组件逻辑
</script>

<style scoped>
#app {
  min-height: 100vh;
}
</style>`;
        await fs.writeFile(path.join(projectPath, 'src/App.vue'), appVue);
        createdFiles.push('src/App.vue');
        // 生成基础Home页面
        const homeVue = `<template>
  <div class="home">
    <h1>欢迎使用 ${projectName}</h1>
    <p>这是一个基于Vue3的项目</p>
  </div>
</template>

<script setup lang="ts">
// Home页面逻辑
</script>

<style scoped>
.home {
  padding: 20px;
  text-align: center;
}

h1 {
  color: #2c3e50;
  margin-bottom: 20px;
}
</style>`;
        await fs.writeFile(path.join(projectPath, 'src/views/Home.vue'), homeVue);
        createdFiles.push('src/views/Home.vue');
        return {
            success: true,
            projectPath,
            message: `Vue3项目 "${projectName}" 生成成功！`,
            files: createdFiles
        };
    }
    catch (error) {
        logger.error({ error, projectPath }, 'Failed to create Vue3 project structure');
        throw new Error(`Failed to create project structure: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * 安装项目依赖
 */
export async function installProjectDependencies(projectPath) {
    try {
        logger.info({ projectPath }, 'Installing project dependencies');
        // 使用pnpm安装依赖
        const { stdout, stderr } = await execAsync('pnpm install', {
            cwd: projectPath,
            timeout: 300000 // 5分钟超时
        });
        logger.info({ stdout, stderr }, 'Dependencies installation completed');
        return {
            success: true,
            message: '依赖安装成功！'
        };
    }
    catch (error) {
        logger.error({ error, projectPath }, 'Failed to install dependencies');
        return {
            success: false,
            message: `依赖安装失败: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
}
