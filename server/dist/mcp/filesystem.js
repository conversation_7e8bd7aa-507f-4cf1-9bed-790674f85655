import * as path from "node:path";
import { experimental_createMCPClient as createMCPClient } from 'ai';
import { Experimental_StdioMCPTransport as StdioMCPTransport } from 'ai/mcp-stdio';
export const initFilesystemMcpClient = async () => {
    const mcpClient = await createMCPClient({
        transport: new StdioMCPTransport({
            command: "npx",
            args: [
                "-y",
                "@modelcontextprotocol/server-filesystem",
                path.resolve(process.cwd(), './repos')
            ],
        }),
    });
    const tools = await mcpClient.tools();
    return tools;
};
