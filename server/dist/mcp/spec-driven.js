import { experimental_createMCPClient as createMCPClient } from 'ai';
import { Experimental_StdioMCPTransport as StdioMCPTransport } from 'ai/mcp-stdio';
export const initSpecDrivenMcpClient = async () => {
    const mcpClient = await createMCPClient({
        transport: new StdioMCPTransport({
            command: "npx",
            args: [
                "-y",
                "@pimzino/spec-workflow-mcp@latest",
            ],
        }),
    });
    console.log('mcpClient', mcpClient);
    const tools = await mcpClient.tools();
    return tools;
};
