import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { logger as pinoLogger } from './utils/logger.js';
import { aiRoutes } from './routes/ai.routes.js';
import { initMcpClient } from './mcp/index.js';

const app = new Hono();

// 注册AI路由
app.route('/ai', aiRoutes);


// 404
app.notFound((c) => c.json({ error: 'Not Found' }, 404));

// error handling
app.onError((err, c) => {
  pinoLogger().error({ err }, 'Unhandled error');
  return c.json({ error: 'Internal Server Error' }, 500);
});

const port = Number(process.env.PORT || 3000);


pinoLogger().info({ port }, `Server listening on http://localhost:${port}`);


const start = async () => {
  await initMcpClient();
  serve({
    fetch: app.fetch,
    port,
  });
}
start()