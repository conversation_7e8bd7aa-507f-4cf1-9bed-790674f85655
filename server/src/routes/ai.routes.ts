import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { generateRequirementDescription, generateDesignAndTasks } from '../services/ai.service.js';
import { logger as pinoLogger } from '../utils/logger.js';

const logger = pinoLogger();
const aiRoutes = new Hono();

// 请求体验证schema
const generateRequirementSchema = z.object({
  input: z.string().min(1, 'User input cannot be empty').max(1000, 'User input too long'),
});

const generateDesignSchema = z.object({
  requirementDescription: z.string().min(1, 'Requirement description cannot be empty').max(10000, 'Requirement description too long'),
});

const generateProjectSchema = z.object({
  designAndTasks: z.string().min(1, 'Design and tasks cannot be empty').max(20000, 'Design and tasks too long'),
  projectName: z.string().min(1, 'Project name cannot be empty').max(50, 'Project name too long').regex(/^[a-zA-Z0-9-_]+$/, 'Project name can only contain letters, numbers, hyphens and underscores'),
});

/**
 * POST /ai/generate-requirement
 * 将用户的自然语言输入转换为详细的需求描述
 */
aiRoutes.post(
  '/generate-requirement',
  zValidator('json', generateRequirementSchema),
  async (c) => {
    try {
      const { input } = c.req.valid('json');
      
      logger.info({ input }, 'Received requirement generation request');

      const requirementDescription = await generateRequirementDescription(input);

      return c.json({
        success: true,
        data: {
          input,
          requirementDescription,
          generatedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error({ error }, 'Failed to generate requirement description');
      
      return c.json({
        success: false,
        error: 'Failed to generate requirement description',
        message: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
);

/**
 * POST /ai/generate-design
 * 基于需求描述生成设计方案和具体任务
 */
aiRoutes.post(
  '/generate-design',
  zValidator('json', generateDesignSchema),
  async (c) => {
    try {
      const { requirementDescription } = c.req.valid('json');

      logger.info({ requirementLength: requirementDescription.length }, 'Received design generation request');

      const designAndTasks = await generateDesignAndTasks(requirementDescription);

      return c.json({
        success: true,
        data: {
          requirementDescription,
          designAndTasks,
          generatedAt: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error({ error }, 'Failed to generate design and tasks');

      return c.json({
        success: false,
        error: 'Failed to generate design and tasks',
        message: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
);

/**
 * POST /ai/generate-project
 * 基于设计方案生成Vue3项目代码
 */
// aiRoutes.post(
//   '/generate-project',
//   zValidator('json', generateProjectSchema),
//   async (c) => {
//     try {
//       const { designAndTasks, projectName } = c.req.valid('json');

//       logger.info({ designLength: designAndTasks.length, projectName }, 'Received project generation request');

//       const result = await generateVue3Project(designAndTasks, projectName);

//       return c.json({
//         success: true,
//         data: {
//           projectName,
//           projectPath: result.projectPath,
//           message: result.message,
//           files: result.files,
//           generatedAt: new Date().toISOString(),
//         },
//       });
//     } catch (error) {
//       logger.error({ error }, 'Failed to generate Vue3 project');

//       return c.json({
//         success: false,
//         error: 'Failed to generate Vue3 project',
//         message: error instanceof Error ? error.message : 'Unknown error',
//       }, 500);
//     }
//   }
// );


/**
 * GET /ai/info
 * 获取AI服务信息
 */
aiRoutes.get('/info', (c) => {
  return c.json({
    success: true,
    data: {
      model: 'deepseek/deepseek-r1',
      provider: 'OpenRouter',
      description: 'AI service for generating detailed requirement descriptions from natural language input',
      endpoints: [
        {
          method: 'POST',
          path: '/ai/generate-requirement',
          description: 'Generate detailed requirement description from user input',
        },
        {
          method: 'POST',
          path: '/ai/generate-design',
          description: 'Generate design and task breakdown from requirement description',
        },
        {
          method: 'POST',
          path: '/ai/generate-project',
          description: 'Generate Vue3 project files from design and tasks',
        },
        {
          method: 'GET',
          path: '/ai/health',
          description: 'Check AI service health status',
        },
        {
          method: 'GET',
          path: '/ai/info',
          description: 'Get AI service information',
        },
      ],
    },
  });
});

export { aiRoutes };
