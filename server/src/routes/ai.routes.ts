import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { generateRequirementDescription, generateDesignAndTasks } from '../services/ai.service.js';
import { authMiddleware, optionalAuthMiddleware } from '../middleware/auth.middleware.js';
import { logger as pinoLogger } from '../utils/logger.js';

const logger = pinoLogger();
const aiRoutes = new Hono();

// 请求体验证schema
const generateRequirementSchema = z.object({
  input: z.string().min(1, 'User input cannot be empty').max(1000, 'User input too long'),
});

const generateDesignSchema = z.object({
  requirementDescription: z.string().min(1, 'Requirement description cannot be empty').max(10000, 'Requirement description too long'),
});

const generateProjectSchema = z.object({
  designAndTasks: z.string().min(1, 'Design and tasks cannot be empty').max(20000, 'Design and tasks too long'),
  projectName: z.string().min(1, 'Project name cannot be empty').max(50, 'Project name too long').regex(/^[a-zA-Z0-9-_]+$/, 'Project name can only contain letters, numbers, hyphens and underscores'),
});

/**
 * POST /ai/generate-requirement
 * 将用户的自然语言输入转换为详细的需求描述（需要认证）
 */
aiRoutes.post(
  '/generate-requirement',
  authMiddleware,
  zValidator('json', generateRequirementSchema),
  async (c) => {
    try {
      const { input } = c.req.valid('json');
      const user = c.user!; // authMiddleware确保用户存在

      logger.info({
        input,
        userId: user.id,
        username: user.username
      }, 'Received requirement generation request');

      const requirementDescription = await generateRequirementDescription(input);

      return c.json({
        success: true,
        data: {
          input,
          requirementDescription,
          generatedAt: new Date().toISOString(),
          generatedBy: {
            userId: user.id,
            username: user.username,
          },
        },
      });
    } catch (error) {
      logger.error({ error, userId: c.user?.id }, 'Failed to generate requirement description');

      return c.json({
        success: false,
        error: 'Failed to generate requirement description',
        message: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
);

/**
 * POST /ai/generate-design
 * 基于需求描述生成设计方案和具体任务（需要认证）
 */
aiRoutes.post(
  '/generate-design',
  authMiddleware,
  zValidator('json', generateDesignSchema),
  async (c) => {
    try {
      const { requirementDescription } = c.req.valid('json');
      const user = c.user!; // authMiddleware确保用户存在

      logger.info({
        requirementLength: requirementDescription.length,
        userId: user.id,
        username: user.username
      }, 'Received design generation request');

      const designAndTasks = await generateDesignAndTasks(requirementDescription);

      return c.json({
        success: true,
        data: {
          requirementDescription,
          designAndTasks,
          generatedAt: new Date().toISOString(),
          generatedBy: {
            userId: user.id,
            username: user.username,
          },
        },
      });
    } catch (error) {
      logger.error({ error, userId: c.user?.id }, 'Failed to generate design and tasks');

      return c.json({
        success: false,
        error: 'Failed to generate design and tasks',
        message: error instanceof Error ? error.message : 'Unknown error',
      }, 500);
    }
  }
);

/**
 * POST /ai/generate-project
 * 基于设计方案生成Vue3项目代码
 */
// aiRoutes.post(
//   '/generate-project',
//   zValidator('json', generateProjectSchema),
//   async (c) => {
//     try {
//       const { designAndTasks, projectName } = c.req.valid('json');

//       logger.info({ designLength: designAndTasks.length, projectName }, 'Received project generation request');

//       const result = await generateVue3Project(designAndTasks, projectName);

//       return c.json({
//         success: true,
//         data: {
//           projectName,
//           projectPath: result.projectPath,
//           message: result.message,
//           files: result.files,
//           generatedAt: new Date().toISOString(),
//         },
//       });
//     } catch (error) {
//       logger.error({ error }, 'Failed to generate Vue3 project');

//       return c.json({
//         success: false,
//         error: 'Failed to generate Vue3 project',
//         message: error instanceof Error ? error.message : 'Unknown error',
//       }, 500);
//     }
//   }
// );


/**
 * GET /ai/info
 * 获取AI服务信息（可选认证）
 */
aiRoutes.get('/info', optionalAuthMiddleware, (c) => {
  const user = c.user;

  return c.json({
    success: true,
    data: {
      model: 'MCP Spec-Workflow',
      provider: 'Pimzino Spec-Workflow-MCP',
      description: 'AI service using MCP for spec-driven development workflow',
      authenticated: !!user,
      user: user ? {
        id: user.id,
        username: user.username,
        email: user.email,
      } : null,
      endpoints: [
        {
          method: 'POST',
          path: '/ai/generate-requirement',
          description: 'Generate detailed requirement specification from user input',
          requiresAuth: true,
        },
        {
          method: 'POST',
          path: '/ai/generate-design',
          description: 'Generate design and task breakdown from requirement description',
          requiresAuth: true,
        },
        {
          method: 'POST',
          path: '/ai/generate-project',
          description: 'Generate Vue3 project files from design and tasks',
          requiresAuth: true,
          status: 'disabled',
        },
        {
          method: 'GET',
          path: '/ai/info',
          description: 'Get AI service information',
          requiresAuth: false,
        },
      ],
      authEndpoints: [
        {
          method: 'POST',
          path: '/auth/register',
          description: 'Register a new user account',
        },
        {
          method: 'POST',
          path: '/auth/login',
          description: 'Login with email and password',
        },
        {
          method: 'POST',
          path: '/auth/refresh',
          description: 'Refresh access token using refresh token',
        },
        {
          method: 'POST',
          path: '/auth/logout',
          description: 'Logout and invalidate refresh tokens',
          requiresAuth: true,
        },
        {
          method: 'GET',
          path: '/auth/me',
          description: 'Get current user information',
          requiresAuth: true,
        },
      ],
    },
  });
});

export { aiRoutes };
