import { initSpecDrivenMcpClient } from './spec-driven.js';
import { initFilesystemMcpClient } from './filesystem.js';
const mcpTools: any = {};
export const initMcpClient = async () => {
    const tools = await Promise.all([
        // initSpecDrivenMcpClient(),
        // initFilesystemMcpClient(),
    ]);
    // tools.forEach((tool) => {
    //     Object.assign(mcpTools, {
    //         ...tool,
    //     });
    // });

    console.log('all mcp tools ====>>>>>> ', Object.keys(mcpTools));

    return tools;
}
export const getAllTools = () => mcpTools;