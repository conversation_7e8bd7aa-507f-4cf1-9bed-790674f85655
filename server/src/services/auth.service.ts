import type {
  UserCreateInput,
  UserLoginInput,
  AuthResponse,
  UserResponse
} from '../types/auth.types.js';
import { 
  createUser, 
  authenticateUser, 
  getUserById,
  getUserTokenVersion,
  incrementUserTokenVersion 
} from './user.service.js';
import { 
  generateAccessToken, 
  generateRefreshToken, 
  verifyRefreshToken 
} from '../utils/jwt.js';
import { validatePasswordStrength } from '../utils/password.js';
import { logger as pinoLogger } from '../utils/logger.js';

const logger = pinoLogger();

/**
 * 用户注册
 */
export async function registerUser(userData: UserCreateInput): Promise<AuthResponse> {
  try {
    // 验证输入数据
    if (!userData.username || !userData.email || !userData.password) {
      throw new Error('用户名、邮箱和密码都是必填项');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      throw new Error('邮箱格式不正确');
    }

    // 验证用户名格式
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(userData.username)) {
      throw new Error('用户名只能包含字母、数字和下划线，长度3-20位');
    }

    // 验证密码强度
    const passwordValidation = validatePasswordStrength(userData.password);
    if (!passwordValidation.isValid) {
      throw new Error(`密码不符合要求: ${passwordValidation.errors.join(', ')}`);
    }

    // 创建用户
    const user = await createUser(userData);

    // 生成令牌
    const accessToken = generateAccessToken({
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      tokenVersion: getUserTokenVersion(user.id),
    });

    logger.info({ userId: user.id, username: user.username }, 'User registered successfully');

    return {
      user,
      token: accessToken,
      refreshToken,
    };
  } catch (error) {
    logger.error({ error, userData: { ...userData, password: '[REDACTED]' } }, 'User registration failed');
    throw error;
  }
}

/**
 * 用户登录
 */
export async function loginUser(loginData: UserLoginInput): Promise<AuthResponse> {
  try {
    // 验证输入数据
    if (!loginData.email || !loginData.password) {
      throw new Error('邮箱和密码都是必填项');
    }

    // 验证用户凭据
    const user = await authenticateUser(loginData.email, loginData.password);
    if (!user) {
      throw new Error('邮箱或密码错误');
    }

    // 生成令牌
    const accessToken = generateAccessToken({
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      tokenVersion: getUserTokenVersion(user.id),
    });

    logger.info({ userId: user.id, username: user.username }, 'User logged in successfully');

    return {
      user,
      token: accessToken,
      refreshToken,
    };
  } catch (error) {
    logger.error({ error, email: loginData.email }, 'User login failed');
    throw error;
  }
}

/**
 * 刷新访问令牌
 */
export async function refreshAccessToken(refreshToken: string): Promise<{ token: string; refreshToken: string }> {
  try {
    // 验证刷新令牌
    const payload = verifyRefreshToken(refreshToken);

    // 检查用户是否存在
    const user = await getUserById(payload.userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    // 检查令牌版本
    const currentTokenVersion = getUserTokenVersion(payload.userId);
    if (payload.tokenVersion !== currentTokenVersion) {
      throw new Error('刷新令牌已失效');
    }

    // 生成新的访问令牌
    const newAccessToken = generateAccessToken({
      userId: user.id,
      username: user.username,
      email: user.email,
    });

    // 生成新的刷新令牌
    const newRefreshToken = generateRefreshToken({
      userId: user.id,
      tokenVersion: currentTokenVersion,
    });

    logger.info({ userId: user.id, username: user.username }, 'Access token refreshed successfully');

    return {
      token: newAccessToken,
      refreshToken: newRefreshToken,
    };
  } catch (error) {
    logger.error({ error }, 'Token refresh failed');
    throw error;
  }
}

/**
 * 用户登出（使所有刷新令牌失效）
 */
export async function logoutUser(userId: string): Promise<void> {
  try {
    // 增加令牌版本，使所有刷新令牌失效
    incrementUserTokenVersion(userId);

    logger.info({ userId }, 'User logged out successfully');
  } catch (error) {
    logger.error({ error, userId }, 'User logout failed');
    throw error;
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(userId: string): Promise<UserResponse> {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error('用户不存在');
    }

    return user;
  } catch (error) {
    logger.error({ error, userId }, 'Failed to get current user');
    throw error;
  }
}
