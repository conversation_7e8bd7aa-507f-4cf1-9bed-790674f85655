import { nanoid } from 'nanoid';
import fs from 'fs/promises';
import path from 'path';
import type { User, UserCreateInput, UserResponse } from '../types/auth.types.js';
import { hashPassword, verifyPassword } from '../utils/password.js';
import { logger as pinoLogger } from '../utils/logger.js';

const logger = pinoLogger();

// 数据存储路径
const DATA_DIR = path.join(process.cwd(), 'data');
const USERS_FILE = path.join(DATA_DIR, 'users.json');
const TOKEN_VERSIONS_FILE = path.join(DATA_DIR, 'token-versions.json');

// 内存缓存
let users: Map<string, User> = new Map();
let usersByEmail: Map<string, User> = new Map();
let usersByUsername: Map<string, User> = new Map();
let userTokenVersions: Map<string, number> = new Map();

// 数据结构
interface UserData {
  users: User[];
  tokenVersions: Record<string, number>;
}

/**
 * 创建新用户
 */
export async function createUser(userData: UserCreateInput): Promise<UserResponse> {
  try {
    // 检查邮箱是否已存在
    if (usersByEmail.has(userData.email)) {
      throw new Error('邮箱已被注册');
    }

    // 检查用户名是否已存在
    if (usersByUsername.has(userData.username)) {
      throw new Error('用户名已被使用');
    }

    // 加密密码
    const hashedPassword = await hashPassword(userData.password);

    // 创建用户
    const user: User = {
      id: nanoid(),
      username: userData.username,
      email: userData.email,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 存储用户
    users.set(user.id, user);
    usersByEmail.set(user.email, user);
    usersByUsername.set(user.username, user);
    userTokenVersions.set(user.id, 0);

    logger.info({ userId: user.id, username: user.username }, 'User created successfully');

    // 返回用户信息（不包含密码）
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  } catch (error) {
    logger.error({ error, userData: { ...userData, password: '[REDACTED]' } }, 'Failed to create user');
    throw error;
  }
}

/**
 * 通过邮箱和密码验证用户
 */
export async function authenticateUser(email: string, password: string): Promise<UserResponse | null> {
  try {
    const user = usersByEmail.get(email);
    if (!user) {
      return null;
    }

    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return null;
    }

    logger.info({ userId: user.id, username: user.username }, 'User authenticated successfully');

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  } catch (error) {
    logger.error({ error, email }, 'Failed to authenticate user');
    return null;
  }
}

/**
 * 通过ID获取用户
 */
export async function getUserById(userId: string): Promise<UserResponse | null> {
  const user = users.get(userId);
  if (!user) {
    return null;
  }

  return {
    id: user.id,
    username: user.username,
    email: user.email,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
}

/**
 * 通过邮箱获取用户
 */
export async function getUserByEmail(email: string): Promise<UserResponse | null> {
  const user = usersByEmail.get(email);
  if (!user) {
    return null;
  }

  return {
    id: user.id,
    username: user.username,
    email: user.email,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
}

/**
 * 获取用户令牌版本
 */
export function getUserTokenVersion(userId: string): number {
  return userTokenVersions.get(userId) || 0;
}

/**
 * 增加用户令牌版本（使所有刷新令牌失效）
 */
export function incrementUserTokenVersion(userId: string): void {
  const currentVersion = userTokenVersions.get(userId) || 0;
  userTokenVersions.set(userId, currentVersion + 1);
  logger.info({ userId, newVersion: currentVersion + 1 }, 'User token version incremented');
}

/**
 * 获取所有用户（管理员功能）
 */
export async function getAllUsers(): Promise<UserResponse[]> {
  return Array.from(users.values()).map(user => ({
    id: user.id,
    username: user.username,
    email: user.email,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  }));
}

/**
 * 删除用户
 */
export async function deleteUser(userId: string): Promise<boolean> {
  try {
    const user = users.get(userId);
    if (!user) {
      return false;
    }

    users.delete(userId);
    usersByEmail.delete(user.email);
    usersByUsername.delete(user.username);
    userTokenVersions.delete(userId);

    logger.info({ userId, username: user.username }, 'User deleted successfully');
    return true;
  } catch (error) {
    logger.error({ error, userId }, 'Failed to delete user');
    return false;
  }
}
