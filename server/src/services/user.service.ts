import { nanoid } from 'nanoid';
import fs from 'fs/promises';
import path from 'path';
import type { User, UserCreateInput, UserResponse } from '../types/auth.types.js';
import { hashPassword, verifyPassword } from '../utils/password.js';
import { logger as pinoLogger } from '../utils/logger.js';

const logger = pinoLogger();

// 数据存储路径
const DATA_DIR = path.join(process.cwd(), 'data');
const USERS_FILE = path.join(DATA_DIR, 'users.json');
const TOKEN_VERSIONS_FILE = path.join(DATA_DIR, 'token-versions.json');

// 内存缓存
let users: Map<string, User> = new Map();
let usersByEmail: Map<string, User> = new Map();
let usersByUsername: Map<string, User> = new Map();
let userTokenVersions: Map<string, number> = new Map();

// 数据结构
interface UserData {
  users: User[];
  tokenVersions: Record<string, number>;
}

// 初始化标志
let isInitialized = false;

/**
 * 确保数据目录存在
 */
async function ensureDataDirectory(): Promise<void> {
  try {
    await fs.mkdir(DATA_DIR, { recursive: true });
  } catch (error) {
    logger.error({ error }, 'Failed to create data directory');
    throw error;
  }
}

/**
 * 从文件加载用户数据
 */
async function loadUsersFromFile(): Promise<void> {
  try {
    await ensureDataDirectory();

    // 加载用户数据
    try {
      const usersData = await fs.readFile(USERS_FILE, 'utf-8');
      const userData: UserData = JSON.parse(usersData);

      // 重建内存缓存
      users.clear();
      usersByEmail.clear();
      usersByUsername.clear();
      userTokenVersions.clear();

      userData.users.forEach(user => {
        // 转换日期字符串为Date对象
        const userWithDates: User = {
          ...user,
          createdAt: new Date(user.createdAt),
          updatedAt: new Date(user.updatedAt),
        };

        users.set(user.id, userWithDates);
        usersByEmail.set(user.email, userWithDates);
        usersByUsername.set(user.username, userWithDates);
      });

      // 加载令牌版本
      Object.entries(userData.tokenVersions).forEach(([userId, version]) => {
        userTokenVersions.set(userId, version);
      });

      logger.info({ userCount: userData.users.length }, 'Users loaded from file');
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        logger.info('Users file not found, starting with empty database');
      } else {
        throw error;
      }
    }
  } catch (error) {
    logger.error({ error }, 'Failed to load users from file');
    throw error;
  }
}

/**
 * 保存用户数据到文件
 */
async function saveUsersToFile(): Promise<void> {
  try {
    await ensureDataDirectory();

    const userData: UserData = {
      users: Array.from(users.values()),
      tokenVersions: Object.fromEntries(userTokenVersions.entries()),
    };

    await fs.writeFile(USERS_FILE, JSON.stringify(userData, null, 2), 'utf-8');
    logger.debug('Users saved to file');
  } catch (error) {
    logger.error({ error }, 'Failed to save users to file');
    throw error;
  }
}

/**
 * 创建默认管理员账号
 */
async function createDefaultAdmin(): Promise<void> {
  try {
    const adminEmail = '<EMAIL>';
    const adminUsername = 'admin';
    const adminPassword = '123456';

    // 检查管理员是否已存在
    if (usersByUsername.has(adminUsername)) {
      logger.info('Default admin user already exists');
      return;
    }

    // 创建管理员账号
    const hashedPassword = await hashPassword(adminPassword);
    const adminUser: User = {
      id: nanoid(),
      username: adminUsername,
      email: adminEmail,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 添加到缓存
    users.set(adminUser.id, adminUser);
    usersByEmail.set(adminUser.email, adminUser);
    usersByUsername.set(adminUser.username, adminUser);
    userTokenVersions.set(adminUser.id, 0);

    // 保存到文件
    await saveUsersToFile();

    logger.info({
      userId: adminUser.id,
      username: adminUser.username,
      email: adminUser.email
    }, 'Default admin user created');
  } catch (error) {
    logger.error({ error }, 'Failed to create default admin user');
    throw error;
  }
}

/**
 * 初始化用户服务
 */
async function initializeUserService(): Promise<void> {
  if (isInitialized) {
    return;
  }

  try {
    await loadUsersFromFile();
    await createDefaultAdmin();
    isInitialized = true;
    logger.info('User service initialized');
  } catch (error) {
    logger.error({ error }, 'Failed to initialize user service');
    throw error;
  }
}

/**
 * 创建新用户
 */
export async function createUser(userData: UserCreateInput): Promise<UserResponse> {
  try {
    // 检查邮箱是否已存在
    if (usersByEmail.has(userData.email)) {
      throw new Error('邮箱已被注册');
    }

    // 检查用户名是否已存在
    if (usersByUsername.has(userData.username)) {
      throw new Error('用户名已被使用');
    }

    // 加密密码
    const hashedPassword = await hashPassword(userData.password);

    // 创建用户
    const user: User = {
      id: nanoid(),
      username: userData.username,
      email: userData.email,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 存储用户
    users.set(user.id, user);
    usersByEmail.set(user.email, user);
    usersByUsername.set(user.username, user);
    userTokenVersions.set(user.id, 0);

    logger.info({ userId: user.id, username: user.username }, 'User created successfully');

    // 返回用户信息（不包含密码）
    return {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  } catch (error) {
    logger.error({ error, userData: { ...userData, password: '[REDACTED]' } }, 'Failed to create user');
    throw error;
  }
}

/**
 * 通过邮箱和密码验证用户
 */
export async function authenticateUser(email: string, password: string): Promise<UserResponse | null> {
  try {
    const user = usersByEmail.get(email);
    if (!user) {
      return null;
    }

    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return null;
    }

    logger.info({ userId: user.id, username: user.username }, 'User authenticated successfully');

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  } catch (error) {
    logger.error({ error, email }, 'Failed to authenticate user');
    return null;
  }
}

/**
 * 通过ID获取用户
 */
export async function getUserById(userId: string): Promise<UserResponse | null> {
  const user = users.get(userId);
  if (!user) {
    return null;
  }

  return {
    id: user.id,
    username: user.username,
    email: user.email,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
}

/**
 * 通过邮箱获取用户
 */
export async function getUserByEmail(email: string): Promise<UserResponse | null> {
  const user = usersByEmail.get(email);
  if (!user) {
    return null;
  }

  return {
    id: user.id,
    username: user.username,
    email: user.email,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
}

/**
 * 获取用户令牌版本
 */
export function getUserTokenVersion(userId: string): number {
  return userTokenVersions.get(userId) || 0;
}

/**
 * 增加用户令牌版本（使所有刷新令牌失效）
 */
export function incrementUserTokenVersion(userId: string): void {
  const currentVersion = userTokenVersions.get(userId) || 0;
  userTokenVersions.set(userId, currentVersion + 1);
  logger.info({ userId, newVersion: currentVersion + 1 }, 'User token version incremented');
}

/**
 * 获取所有用户（管理员功能）
 */
export async function getAllUsers(): Promise<UserResponse[]> {
  return Array.from(users.values()).map(user => ({
    id: user.id,
    username: user.username,
    email: user.email,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  }));
}

/**
 * 删除用户
 */
export async function deleteUser(userId: string): Promise<boolean> {
  try {
    const user = users.get(userId);
    if (!user) {
      return false;
    }

    users.delete(userId);
    usersByEmail.delete(user.email);
    usersByUsername.delete(user.username);
    userTokenVersions.delete(userId);

    logger.info({ userId, username: user.username }, 'User deleted successfully');
    return true;
  } catch (error) {
    logger.error({ error, userId }, 'Failed to delete user');
    return false;
  }
}
