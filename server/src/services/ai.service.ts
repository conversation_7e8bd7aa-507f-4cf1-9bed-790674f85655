import { logger as pinoLogger } from '../utils/logger.js';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateText, streamText } from 'ai';
import { getAllTools } from '../mcp/index.js';
const tools = getAllTools()
const openrouter = createOpenRouter({
  apiKey:
    '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',
});
const logger = pinoLogger();
const model = openrouter('deepseek/deepseek-chat-v3-0324:free');
const systemPrompts = `You are an AI assistant specialized in spec-driven development.

## CRITICAL FIRST STEP
**ALWAYS call the spec-workflow-guide tool FIRST** when users request:
- Spec creation or feature development
- Any mention of specifications, requirements, design, or tasks
- Working on new features or project planning

The spec-workflow-guide tool contains the complete workflow instructions that MUST be followed exactly.

## Key Principles
- Use ONLY the provided MCP tools - never create documents manually
- Follow the exact workflow sequence: Requirements → Design → Tasks → Implementation
- Request user approval after EACH document before proceeding
- Never skip steps or phases
`
export const generateRequirementDescription = async (input: string) => {
  const messages: any = [{
    role: 'system',
    content: systemPrompts,
  }, {
    role: 'user',
    content: input
  }];
  while (true) {
    const result: any = streamText({
      model,
      messages,
      tools,
    });
    logger.info({ result }, 'result');
    // Stream the response (only necessary for providing updates to the user)
    for await (const chunk of result.fullStream) {
      logger.info({ chunk }, 'chunk');
      if (chunk.type === 'text-delta') {
        process.stdout.write(chunk.text);
      }

      if (chunk.type === 'tool-call') {
        console.log('\\nCalling tool:', chunk.toolName);
      }
    }
    // Add LLM generated messages to the message history
    const responseMessages = (await result.response).messages;
    messages.push(...responseMessages);

    const finishReason = await result.finishReason;
    logger.info({ finishReason }, 'finishReason');
    if (finishReason === 'tool-calls') {
      const toolCalls = await result.toolCalls;

      // Handle all tool call execution here
      for (const toolCall of toolCalls) {
        const { toolName } = toolCall
        logger.info({ toolCall }, 'Calling tool');
        const toolOutput = await tools[toolName](toolCall.input);
        messages.push({
          role: 'tool',
          content: [
            {
              toolName: toolCall.toolName,
              toolCallId: toolCall.toolCallId,
              type: 'tool-result',
              output: { type: 'text', value: toolOutput }, // update depending on the tool's output format
            },
          ],
        });
        logger.info({ toolCall }, 'Calling tool finished');
        // Handle other tool calls
      }
    } else {
      // Exit the loop when the model doesn't request to use any more tools
      console.log('\\n\\nFinal message history:');
      console.dir(messages, { depth: null });
      break;
    }
  }
}

export const generateDesignAndTasks = async (specification: string) => {

}