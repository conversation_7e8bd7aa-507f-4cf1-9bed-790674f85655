import { generateText } from 'ai';
import { logger as pinoLogger } from '../utils/logger.js';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { createVue3ProjectStructure, installProjectDependencies } from './project-generator.service.js';
const logger = pinoLogger();

// 配置OpenRouter的DeepSeek R1 Free模型
const openrouter = createOpenRouter({
  apiKey:
    '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',
});
const model = openrouter('deepseek/deepseek-chat-v3-0324:free');

/**
 * 将用户的自然语言输入转换为详细的需求描述
 * @param userInput 用户的自然语言输入
 * @returns 详细的需求描述
 */
export async function generateRequirementDescription(userInput: string): Promise<string> {
  try {
    logger.info({ userInput }, 'Processing user input for requirement generation');

    const prompt = `你是一个专业的前端产品需求分析师，专门为Vue3 + TypeScript + Composition API项目生成详细的开发需求。请将用户的简单自然语言描述转换为详细、具体、可执行的前端开发需求。

用户输入: "${userInput}"

请按照以下格式输出详细的前端开发需求描述：

## 功能概述
[简要描述功能的核心目标和用户价值]

## 页面/组件设计
### 页面结构
- [主要页面列表]
- [页面间的导航关系]
- [页面层级结构]

### 组件拆分
- [主要组件列表]
- [组件职责说明]
- [组件间的数据传递关系]

## 用户界面需求
### 布局设计
- [页面布局方式（如：响应式、固定宽度等）]
- [主要区域划分]
- [移动端适配要求]

### 交互设计
- [用户操作流程]
- [表单交互方式]
- [反馈机制（loading、成功、错误状态）]
- [动画效果要求]

### 视觉设计
- [UI风格要求（如：现代简约、卡片式等）]
- [色彩方案建议]
- [字体和间距规范]

## 技术实现需求
### Vue3技术栈
- [使用的Vue3特性（Composition API、响应式等）]
- [状态管理方案（Pinia/Vuex）]
- [路由配置需求]

### 数据处理
- [API接口需求]
- [数据结构定义]
- [数据验证规则]
- [缓存策略]

### 第三方依赖
- [UI组件库选择建议]
- [工具库需求]
- [插件需求]

## 开发任务拆解
### 核心功能模块
1. [模块1名称] - [具体实现要求]
2. [模块2名称] - [具体实现要求]
3. [模块3名称] - [具体实现要求]

### 开发优先级
- P0（核心功能）：[必须实现的功能]
- P1（重要功能）：[重要但可延后的功能]
- P2（优化功能）：[体验优化功能]

## 用户故事
作为 [用户角色]，我希望能够 [具体操作]，以便 [达成目标]。

## 验收标准
### 功能验收
1. [功能点1的验收条件]
2. [功能点2的验收条件]
3. [功能点3的验收条件]

### 性能验收
- [页面加载时间要求]
- [交互响应时间要求]
- [内存使用要求]

### 兼容性验收
- [浏览器兼容性要求]
- [设备兼容性要求]

## 开发注意事项
### 代码规范
- [Vue3 Composition API最佳实践]
- [TypeScript类型定义要求]
- [组件命名和文件组织规范]

### 性能优化
- [懒加载策略]
- [代码分割建议]
- [缓存优化方案]

### 可维护性
- [组件复用性考虑]
- [代码可读性要求]
- [测试覆盖要求]

请确保需求描述：
1. 具体明确，可直接指导Vue3开发
2. 考虑前端开发的技术特点和限制
3. 包含完整的用户体验设计
4. 提供清晰的开发任务拆解
5. 考虑性能和可维护性

现在请基于用户输入生成详细的前端开发需求描述：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.7,
    });

    logger.info({ 
      inputLength: userInput.length, 
      outputLength: result.text.length 
    }, 'Successfully generated requirement description');

    return result.text;
  } catch (error) {
    logger.error({ error, userInput }, 'Failed to generate requirement description');
    throw new Error('Failed to generate requirement description');
  }
}

/**
 * 基于需求描述生成设计方案和具体任务
 * @param requirementDescription 详细的需求描述
 * @returns 设计方案和任务列表
 */
export async function generateDesignAndTasks(requirementDescription: string): Promise<string> {
  try {
    logger.info({ requirementLength: requirementDescription.length }, 'Processing requirement for design and task generation');

    const prompt = `你是一个资深的前端架构师和项目经理，专门负责将需求描述转换为具体的设计方案和开发任务。请基于提供的需求描述，生成详细的设计方案和可执行的开发任务列表。

需求描述:
${requirementDescription}

请按照以下格式输出设计方案和任务拆分：

## 技术架构设计

### 项目结构设计
\`\`\`
src/
├── components/          # 通用组件
│   ├── ui/             # 基础UI组件
│   └── business/       # 业务组件
├── views/              # 页面组件
├── composables/        # 组合式函数
├── stores/             # 状态管理
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── api/                # API接口
└── assets/             # 静态资源
\`\`\`

### 核心技术选型
- **框架**: Vue 3 + TypeScript + Vite
- **状态管理**: [推荐的状态管理方案]
- **UI组件库**: [推荐的UI库]
- **路由**: Vue Router 4
- **HTTP客户端**: [推荐的HTTP库]
- **样式方案**: [推荐的样式解决方案]

### 数据流设计
- [描述数据在应用中的流动方式]
- [状态管理策略]
- [API调用策略]

## 详细设计方案

### 页面设计
#### [页面1名称]
- **路由**: \`/[路由路径]\`
- **组件结构**: [描述页面的组件层次]
- **状态管理**: [页面级状态和全局状态]
- **API接口**: [需要调用的接口]

#### [页面2名称]
- **路由**: \`/[路由路径]\`
- **组件结构**: [描述页面的组件层次]
- **状态管理**: [页面级状态和全局状态]
- **API接口**: [需要调用的接口]

### 组件设计
#### [组件1名称]
- **功能**: [组件的主要功能]
- **Props**: [输入属性定义]
- **Events**: [输出事件定义]
- **插槽**: [插槽设计]

#### [组件2名称]
- **功能**: [组件的主要功能]
- **Props**: [输入属性定义]
- **Events**: [输出事件定义]
- **插槽**: [插槽设计]

### API接口设计
#### [接口1]
- **方法**: GET/POST/PUT/DELETE
- **路径**: \`/api/[路径]\`
- **参数**: [请求参数]
- **响应**: [响应数据结构]

#### [接口2]
- **方法**: GET/POST/PUT/DELETE
- **路径**: \`/api/[路径]\`
- **参数**: [请求参数]
- **响应**: [响应数据结构]

## 开发任务拆分

### 阶段一：项目基础搭建 (预计1-2天)
- [ ] **任务1.1**: 项目初始化和依赖安装
  - 创建Vue3项目结构
  - 安装必要依赖包
  - 配置TypeScript和ESLint
  - **预计时间**: 2小时
  - **负责人**: [开发者]
  - **验收标准**: 项目能正常启动，无编译错误

- [ ] **任务1.2**: 基础配置和工具设置
  - 配置路由结构
  - 设置状态管理
  - 配置API客户端
  - **预计时间**: 3小时
  - **负责人**: [开发者]
  - **验收标准**: 基础架构搭建完成

### 阶段二：核心功能开发 (预计3-5天)
- [ ] **任务2.1**: [具体功能模块1]
  - 实现[具体功能描述]
  - 创建相关组件
  - 实现API调用
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

- [ ] **任务2.2**: [具体功能模块2]
  - 实现[具体功能描述]
  - 创建相关组件
  - 实现API调用
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

### 阶段三：UI/UX优化 (预计2-3天)
- [ ] **任务3.1**: 界面美化和交互优化
  - 实现响应式设计
  - 添加动画效果
  - 优化用户体验
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

### 阶段四：测试和优化 (预计1-2天)
- [ ] **任务4.1**: 功能测试和bug修复
  - 单元测试编写
  - 集成测试
  - 性能优化
  - **预计时间**: [具体时间]
  - **负责人**: [开发者]
  - **验收标准**: [具体验收条件]

## 关键里程碑
1. **MVP版本** (第3天): 核心功能可用
2. **Beta版本** (第6天): 功能完整，待优化
3. **正式版本** (第8天): 完成测试，可上线

## 风险评估和应对
### 技术风险
- **风险**: [具体技术风险]
- **影响**: [风险影响评估]
- **应对**: [应对措施]

### 进度风险
- **风险**: [具体进度风险]
- **影响**: [风险影响评估]
- **应对**: [应对措施]

## 开发规范
### 代码规范
- 使用Vue3 Composition API
- 严格的TypeScript类型检查
- 统一的组件命名规范
- 清晰的文件组织结构

### Git工作流
- 功能分支开发
- 代码审查流程
- 提交信息规范

请确保设计方案：
1. 技术架构清晰合理
2. 任务拆分具体可执行
3. 时间估算相对准确
4. 考虑了技术风险和应对措施
5. 符合Vue3最佳实践

现在请基于需求描述生成详细的设计方案和任务拆分：`;

    const result = await generateText({
      model: model,
      prompt,
      temperature: 0.7,
    });

    logger.info({
      requirementLength: requirementDescription.length,
      outputLength: result.text.length
    }, 'Successfully generated design and tasks');

    return result.text;
  } catch (error) {
    logger.error({ error, requirementDescription }, 'Failed to generate design and tasks');
    throw new Error('Failed to generate design and tasks');
  }
}

/**
 * 基于设计方案生成Vue3项目代码和安装依赖
 * @param designAndTasks 设计方案和任务描述
 * @param projectName 项目名称
 * @returns 项目生成结果
 */
export async function generateVue3Project(designAndTasks: string, projectName: string): Promise<{
  success: boolean;
  projectPath: string;
  message: string;
  files: string[];
}> {
  try {
    logger.info({ designLength: designAndTasks.length, projectName }, 'Starting Vue3 project generation');

    // 首先通过AI分析设计方案，提取关键信息
    const analysisPrompt = `分析以下设计方案，提取关键信息用于生成Vue3项目：

设计方案:
${designAndTasks}

请以JSON格式返回以下信息：
{
  "dependencies": ["依赖包1", "依赖包2"],
  "devDependencies": ["开发依赖1", "开发依赖2"],
  "components": [
    {
      "name": "组件名",
      "path": "src/components/ComponentName.vue",
      "description": "组件描述"
    }
  ],
  "pages": [
    {
      "name": "页面名",
      "path": "src/views/PageName.vue",
      "route": "/route-path"
    }
  ],
  "stores": [
    {
      "name": "store名",
      "path": "src/stores/storeName.ts"
    }
  ],
  "types": [
    {
      "name": "类型名",
      "interface": "TypeScript接口定义"
    }
  ]
}`;

    const analysisResult = await generateText({
      model: model,
      prompt: analysisPrompt,
      temperature: 0.1,
    });

    let projectConfig;
    try {
      // 尝试解析AI返回的JSON
      const jsonMatch = analysisResult.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        projectConfig = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      logger.warn({ parseError }, 'Failed to parse AI response, using default config');
      // 使用默认配置
      projectConfig = {
        dependencies: ['vue', 'vue-router', 'pinia', 'axios'],
        devDependencies: ['@vitejs/plugin-vue', 'typescript', 'vue-tsc'],
        components: [],
        pages: [],
        stores: [],
        types: []
      };
    }

    // 生成项目结构和代码
    const result = await createVue3ProjectStructure(projectName, projectConfig, designAndTasks);

    logger.info({
      projectPath: result.projectPath,
      filesCount: result.files.length
    }, 'Successfully generated Vue3 project');

    return result;
  } catch (error) {
    logger.error({ error, designAndTasks, projectName }, 'Failed to generate Vue3 project');
    throw new Error(`Failed to generate Vue3 project: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * 健康检查AI服务连接
 */
export async function checkAIServiceHealth(): Promise<boolean> {
  try {
    const result = await generateText({
      model: model,
      prompt: 'Hello, please respond with "OK" if you can receive this message.',
    });

    logger.info({ response: result.text }, 'AI service health check completed');
    return result.text.toLowerCase().includes('ok');
  } catch (error) {
    logger.error({ error }, 'AI service health check failed');
    return false;
  }
}
